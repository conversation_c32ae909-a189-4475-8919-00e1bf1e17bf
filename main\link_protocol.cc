#include <stdio.h>
#include <vector>
#include <string>
#include <mutex>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_event.h"
#include "nvs_flash.h"
#include "esp_log.h"
#include "esp_random.h"
#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include "host/ble_hs.h"
#include "services/gap/ble_svc_gap.h"
#include "sdkconfig.h"
#include <cJSON.h>
#include "link_protocol.h"
extern uint8_t ble_addr_type;
static int scan_device_count = 0;
static const int MAX_SCAN_DEVICES = 10;   // 最大扫描设备数量
static const int SCAN_DURATION_MS = 3000; // 扫描持续时间：3秒

// Link循环相关常量
static const int LINK_CYCLE_DURATION_MS = 500;  // 总周期时间
static const int LINK_SCAN_DURATION_MS = 150;   // 扫描阶段时间
static const int LINK_BACKOFF_MAX_MS = 20;      // 最大随机back-off时间
static const int LINK_ADV_START_MS = 170;       // 广播开始时间 (150 + 最大20ms back-off)

// Link循环状态
static bool g_link_cycle_active = false;
static TaskHandle_t g_link_cycle_task_handle = nullptr;
static std::string g_link_cycle_uuid = "123456781234567889ABCDEF01234567";
static uint8_t g_link_cycle_version = 1;
static std::string g_link_cycle_device_name = "LinkDevice";

// Forward declarations
extern "C" void ble_app_scan(void);

// --- BLE scan result aggregation for MCP tool ---
struct BleDeviceInfo
{
    std::string name;
    std::string addr;
    int rssi;
    std::vector<std::string> uuids128;  // 128-bit UUIDs as hex strings
};
static std::vector<BleDeviceInfo> g_ble_scan_results;
static SemaphoreHandle_t g_ble_scan_done_sem = nullptr;
static std::mutex g_ble_results_mutex;

// --- BLE advertising state ---
static bool g_advertising_active = false;
static uint8_t g_adv_uuid128[16];
static uint8_t g_adv_version = 0;

static inline std::string fmt_addr_str(const ble_addr_t *addr)
{
    char buf[18];
    const uint8_t *a = addr->val;
    snprintf(buf, sizeof(buf), "%02X:%02X:%02X:%02X:%02X:%02X", a[5], a[4], a[3], a[2], a[1], a[0]);
    return std::string(buf);
}

// Helper function to convert 128-bit UUID to hex string
static std::string uuid128_to_hex_string(const uint8_t *uuid)
{
    char buf[33];  // 32 hex chars + null terminator
    for (int i = 0; i < 16; i++) {
        snprintf(buf + i * 2, 3, "%02X", uuid[i]);
    }
    return std::string(buf);
}

// Start scan and block until complete, return JSON string with results
std::string ble_scan_start_and_wait_json()
{
    return ble_scan_start_and_wait_json_with_filter("");
}

// Start scan with UUID filter and block until complete, return JSON string with filtered results
std::string ble_scan_start_and_wait_json_with_filter(const std::string& uuid_filter)
{
    {
        std::lock_guard<std::mutex> lk(g_ble_results_mutex);
        g_ble_scan_results.clear();
    }
    if (g_ble_scan_done_sem == nullptr)
    {
        g_ble_scan_done_sem = xSemaphoreCreateBinary();
    }
    else
    {
        xQueueReset(g_ble_scan_done_sem);
    }
    ble_app_scan();
    // Wait until DISC_COMPLETE event or timeout slightly longer than scan duration
    TickType_t wait_ticks = pdMS_TO_TICKS(SCAN_DURATION_MS + 1000);
    xSemaphoreTake(g_ble_scan_done_sem, wait_ticks);

    // Build JSON with filtering
    cJSON *root = cJSON_CreateObject();
    cJSON *devices = cJSON_CreateArray();
    int count = 0;
    int total_found = 0;
    {
        std::lock_guard<std::mutex> lk(g_ble_results_mutex);
        total_found = (int)g_ble_scan_results.size();

        for (const auto &d : g_ble_scan_results)
        {
            // Apply UUID filter if specified
            bool include_device = true;
            if (!uuid_filter.empty()) {
                include_device = false;
                // Check if any of the device's 128-bit UUIDs match the filter
                for (const auto &uuid : d.uuids128) {
                    if (uuid.find(uuid_filter) != std::string::npos) {
                        include_device = true;
                        break;
                    }
                }
            }

            if (include_device) {
                cJSON *obj = cJSON_CreateObject();
                cJSON_AddStringToObject(obj, "name", d.name.c_str());
                cJSON_AddStringToObject(obj, "addr", d.addr.c_str());
                cJSON_AddNumberToObject(obj, "rssi", d.rssi);

                // Add UUID arrays
                cJSON *uuids128_array = cJSON_CreateArray();
                for (const auto &uuid : d.uuids128) {
                    cJSON_AddItemToArray(uuids128_array, cJSON_CreateString(uuid.c_str()));
                }
                cJSON_AddItemToObject(obj, "uuids128", uuids128_array);

                cJSON *uuids16_array = cJSON_CreateArray();
                for (uint16_t uuid : d.uuids16) {
                    cJSON_AddItemToArray(uuids16_array, cJSON_CreateNumber(uuid));
                }
                cJSON_AddItemToObject(obj, "uuids16", uuids16_array);

                cJSON *uuids32_array = cJSON_CreateArray();
                for (uint32_t uuid : d.uuids32) {
                    cJSON_AddItemToArray(uuids32_array, cJSON_CreateNumber(uuid));
                }
                cJSON_AddItemToObject(obj, "uuids32", uuids32_array);

                cJSON_AddItemToArray(devices, obj);
                count++;
            }
        }
    }
    cJSON_AddBoolToObject(root, "success", true);
    cJSON_AddNumberToObject(root, "count", count);
    cJSON_AddNumberToObject(root, "total_found", total_found);
    if (!uuid_filter.empty()) {
        cJSON_AddStringToObject(root, "uuid_filter", uuid_filter.c_str());
    }
    cJSON_AddItemToObject(root, "devices", devices);

    char *json_str = cJSON_PrintUnformatted(root);
    std::string result(json_str ? json_str : "{}");
    if (json_str)
        cJSON_free(json_str);
    cJSON_Delete(root);
    ESP_LOGD("GAP", "BLE scan result: %s", result.c_str());
    return result;
}

// BLE event handling
static int ble_gap_event(struct ble_gap_event *event, void *arg)
{
    struct ble_hs_adv_fields fields;

    switch (event->type)
    {
    // NimBLE event discovery
    case BLE_GAP_EVENT_DISC:
        ESP_LOGI("GAP", "GAP EVENT DISCOVERY");
        ble_hs_adv_parse_fields(&fields, event->disc.data, event->disc.length_data);

        // Apply complete device filter
        if (!ble_device_filter(&fields)) {
            ESP_LOGD("GAP", "Device filtered out by ble_device_filter");
            break; // Skip this device
        }

        if (fields.name_len > 0)
        {
            printf("Device %d - Name: %.*s\n", scan_device_count + 1, fields.name_len, fields.name);
            printf("=== Advertisement Fields ===\n");
            if (fields.name && fields.name_len > 0)
            {
                printf("Name: %.*s (len: %d)\n", fields.name_len, (char *)fields.name, fields.name_len);
            }
            else
            {
                printf("Name: (null) (len: %d)\n", fields.name_len);
            }
            printf("TX Power: %d\n", fields.tx_pwr_lvl);
            printf("Appearance: 0x%04X\n", fields.appearance);
            printf("Flags: 0x%02X\n", fields.flags);

            // Print UUIDs
            printf("16-bit UUIDs (%d): ", fields.num_uuids16);
            for (int i = 0; i < fields.num_uuids16; i++)
            {
                printf("0x%04X ", ble_uuid_u16(&fields.uuids16[i].u));
            }
            printf("\n");

            printf("32-bit UUIDs (%d): ", fields.num_uuids32);
            for (int i = 0; i < fields.num_uuids32; i++)
            {
                printf("0x%08lX ", (unsigned long)fields.uuids32[i].value);
            }
            printf("\n");

            printf("128-bit UUIDs (%d): ", fields.num_uuids128);
            for (int i = 0; i < fields.num_uuids128; i++)
            {
                printf("0x");
                for (int j = 0; j < 16; j++)
                {
                    printf("%02X", fields.uuids128[i].value[j]);
                }
                printf(" ");
            }
            printf("\n");

            // Print manufacturer data
            if (fields.mfg_data_len > 0)
            {
                printf("Manufacturer Data (%d bytes): ", fields.mfg_data_len);
                for (int i = 0; i < fields.mfg_data_len; i++)
                {
                    printf("%02X ", fields.mfg_data[i]);
                }
                printf("\n");
            }
            // record result
            BleDeviceInfo info;
            info.name = std::string(fields.name, fields.name + fields.name_len);
            info.addr = fmt_addr_str(&event->disc.addr);
            info.rssi = event->disc.rssi;

            // Capture UUID information
            // 16-bit UUIDs
            for (int i = 0; i < fields.num_uuids16; i++) {
                info.uuids16.push_back(ble_uuid_u16(&fields.uuids16[i].u));
            }

            // 32-bit UUIDs
            for (int i = 0; i < fields.num_uuids32; i++) {
                info.uuids32.push_back(fields.uuids32[i].value);
            }

            // 128-bit UUIDs
            for (int i = 0; i < fields.num_uuids128; i++) {
                std::string uuid_hex = uuid128_to_hex_string(fields.uuids128[i].value);
                info.uuids128.push_back(uuid_hex);
            }

            {
                std::lock_guard<std::mutex> lk(g_ble_results_mutex);
                g_ble_scan_results.push_back(std::move(info));
            }
        }
        else
        {
            printf("Device %d - No name available\n", scan_device_count + 1);
            BleDeviceInfo info;
            info.name = std::string("");
            info.addr = fmt_addr_str(&event->disc.addr);
            info.rssi = event->disc.rssi;

            // Capture UUID information even for unnamed devices
            // 16-bit UUIDs
            for (int i = 0; i < fields.num_uuids16; i++) {
                info.uuids16.push_back(ble_uuid_u16(&fields.uuids16[i].u));
            }

            // 32-bit UUIDs
            for (int i = 0; i < fields.num_uuids32; i++) {
                info.uuids32.push_back(fields.uuids32[i].value);
            }

            // 128-bit UUIDs
            for (int i = 0; i < fields.num_uuids128; i++) {
                std::string uuid_hex = uuid128_to_hex_string(fields.uuids128[i].value);
                info.uuids128.push_back(uuid_hex);
            }

            {
                std::lock_guard<std::mutex> lk(g_ble_results_mutex);
                g_ble_scan_results.push_back(std::move(info));
            }
        }

        scan_device_count++;

        // 如果达到最大扫描设备数量，停止扫描
        if (scan_device_count >= MAX_SCAN_DEVICES)
        {
            printf("Reached maximum scan devices (%d), stopping scan...\n", MAX_SCAN_DEVICES);
            ble_gap_disc_cancel();
        }
        break;

    case BLE_GAP_EVENT_DISC_COMPLETE:
        printf("Scan completed. Total devices found: %d\n", scan_device_count);
        printf("Scan finished.\n");
        if (g_ble_scan_done_sem)
        {
            xSemaphoreGive(g_ble_scan_done_sem);
        }
        break;

    default:
        break;
    }
    return 0;
}

extern "C" {

// Filter function to check if manufacturer data matches LINK_PROTOCOL_MANUFACTURER_ID
int is_link_protocol_device(const uint8_t *mfg_data, int mfg_data_len)
{
    // Define the manufacturer ID patterns using header constants
    static const uint8_t link_protocol_v1[] = LINK_PROTOCOL_MANUFACTURER_ID_V1;
    static const uint8_t link_protocol_v2[] = LINK_PROTOCOL_MANUFACTURER_ID_V2;
    static const uint8_t link_protocol_v3[] = LINK_PROTOCOL_MANUFACTURER_ID_V3;

    const int pattern_len = sizeof(link_protocol_v1);

    // Check if manufacturer data length is sufficient
    if (mfg_data == NULL || mfg_data_len < pattern_len) {
        return 0; // Not a match
    }

    // Check against each version
    if (memcmp(mfg_data, link_protocol_v1, pattern_len) == 0) {
        ESP_LOGI("LINK_FILTER", "Found LINK_PROTOCOL_V1 device");
        return 1; // Match found
    }

    if (memcmp(mfg_data, link_protocol_v2, pattern_len) == 0) {
        ESP_LOGI("LINK_FILTER", "Found LINK_PROTOCOL_V2 device");
        return 1; // Match found
    }

    if (memcmp(mfg_data, link_protocol_v3, pattern_len) == 0) {
        ESP_LOGI("LINK_FILTER", "Found LINK_PROTOCOL_V3 device");
        return 1; // Match found
    }

    return 0; // No match found
}

// Complete filter function for BLE devices
int ble_device_filter(const struct ble_hs_adv_fields *fields)
{
    if (fields == NULL) {
        ESP_LOGD("BLE_FILTER", "Fields is NULL");
        return 0; // Filter out
    }

    // 1. 检查制造商数据是否为LINK_PROTOCOL_MANUFACTURER_ID之一
    if (fields->mfg_data_len <= 0 || fields->mfg_data == NULL) {
        ESP_LOGD("BLE_FILTER", "Device filtered out - no manufacturer data");
        return 0; // 过滤掉没有制造商数据的设备
    }

    if (!is_link_protocol_device(fields->mfg_data, fields->mfg_data_len)) {
        ESP_LOGD("BLE_FILTER", "Device filtered out - not a LINK_PROTOCOL device");
        return 0; // 过滤掉不是LINK_PROTOCOL设备的
    }

    // 2. 检查设备名称是否为空
    if (fields->name_len <= 0 || fields->name == NULL) {
        ESP_LOGD("BLE_FILTER", "Device filtered out - no device name");
        return 0; // 过滤掉没有设备名称的设备
    }

    // 3. 检查128位UUID是否为空
    if (fields->num_uuids128 <= 0 || fields->uuids128 == NULL) {
        ESP_LOGD("BLE_FILTER", "Device filtered out - no 128-bit UUIDs");
        return 0; // 过滤掉没有128位UUID的设备
    }

    ESP_LOGI("BLE_FILTER", "Device passed all filters - Name: %.*s, 128-bit UUIDs: %d",
             fields->name_len, (char*)fields->name, fields->num_uuids128);
    return 1; // 通过所有过滤条件
}

void ble_app_scan(void)
{
    printf("Start single scan (duration: %d ms, max devices: %d)...\n", SCAN_DURATION_MS, MAX_SCAN_DEVICES);

    // 重置扫描计数器
    scan_device_count = 0;

    struct ble_gap_disc_params disc_params;
    disc_params.filter_duplicates = 1;
    disc_params.passive = 0;
    disc_params.itvl = 0;
    disc_params.window = 0;
    disc_params.filter_policy = 0;
    disc_params.limited = 0;

    // 使用有限时间扫描而不是永久扫描
    ble_gap_disc(ble_addr_type, SCAN_DURATION_MS, &disc_params, ble_gap_event, NULL);
}

// 短时间扫描函数，用于link循环
void ble_app_scan_short(int duration_ms)
{
    ESP_LOGD("LINK_CYCLE", "Start short scan (duration: %d ms)...", duration_ms);

    // 重置扫描计数器
    scan_device_count = 0;

    struct ble_gap_disc_params disc_params;
    disc_params.filter_duplicates = 1;
    disc_params.passive = 0;
    disc_params.itvl = 0;
    disc_params.window = 0;
    disc_params.filter_policy = 0;
    disc_params.limited = 0;

    // 使用指定时间扫描
    ble_gap_disc(ble_addr_type, duration_ms, &disc_params, ble_gap_event, NULL);
}

// The application
void ble_app_on_sync(void)
{
    ble_hs_id_infer_auto(0, &ble_addr_type); // Determines the best address type automatically
    ble_app_scan();
}

// BLE advertising event handler
static int ble_gap_adv_event(struct ble_gap_event *event, void *arg)
{
    switch (event->type) {
    case BLE_GAP_EVENT_ADV_COMPLETE:
        ESP_LOGI("BLE_ADV", "Advertising complete; reason=%d", event->adv_complete.reason);
        g_advertising_active = false;
        break;
    default:
        break;
    }
    return 0;
}

// Start BLE advertising with custom 128-bit UUID and version
int ble_start_advertising(const uint8_t *uuid128, uint8_t version, const char *device_name)
{
    if (uuid128 == NULL || device_name == NULL) {
        ESP_LOGE("BLE_ADV", "Invalid parameters");
        return -1;
    }

    if (g_advertising_active) {
        ESP_LOGW("BLE_ADV", "Advertising already active, stopping first");
        ble_stop_advertising();
    }

    // Copy UUID and version for later use
    memcpy(g_adv_uuid128, uuid128, 16);
    g_adv_version = version;

    struct ble_gap_adv_params adv_params;
    struct ble_hs_adv_fields fields;
    int rc;

    // Set advertising parameters
    memset(&adv_params, 0, sizeof(adv_params));
    adv_params.conn_mode = BLE_GAP_CONN_MODE_UND;  // Undirected connectable
    adv_params.disc_mode = BLE_GAP_DISC_MODE_GEN;  // General discoverable
    adv_params.itvl_min = BLE_GAP_ADV_FAST_INTERVAL1_MIN;
    adv_params.itvl_max = BLE_GAP_ADV_FAST_INTERVAL1_MAX;

    // Prepare advertisement fields - keep it minimal
    memset(&fields, 0, sizeof(fields));

    // Set flags (3 bytes: length + type + flags)
    fields.flags = BLE_HS_ADV_F_DISC_GEN | BLE_HS_ADV_F_BREDR_UNSUP;

    // Prepare manufacturer data with LINK_PROTOCOL format (9 bytes: length + type + 7 data bytes)
    static uint8_t mfg_data[32];
    static const uint8_t link_protocol_base[] = LINK_PROTOCOL_MANUFACTURER_ID_V1;

    // Copy base manufacturer ID and modify version
    memcpy(mfg_data, link_protocol_base, sizeof(link_protocol_base));
    mfg_data[6] = version; // Set version byte

    fields.mfg_data = mfg_data;
    fields.mfg_data_len = 7; // Base length with version

    // Calculate remaining space: 31 - 3 (flags) - 9 (mfg data) = 19 bytes
    // Use remaining space for device name (17 bytes max for name data)
    size_t name_len = strlen(device_name);
    if (name_len > 15) {
        name_len = 15; // Truncate to fit in remaining space
    }
    fields.name = (uint8_t *)device_name;
    fields.name_len = name_len;
    fields.name_is_complete = (name_len == strlen(device_name)) ? 1 : 0;

    // Set advertisement data
    rc = ble_gap_adv_set_fields(&fields);
    if (rc != 0) {
        ESP_LOGE("BLE_ADV", "Error setting advertisement data; rc=%d", rc);
        return rc;
    }

    // Set scan response data with 128-bit UUID
    struct ble_hs_adv_fields rsp_fields;
    memset(&rsp_fields, 0, sizeof(rsp_fields));

    // Create proper UUID structure
    static ble_uuid128_t service_uuid;
    memcpy(service_uuid.value, uuid128, 16);
    service_uuid.u.type = BLE_UUID_TYPE_128;

    rsp_fields.uuids128 = &service_uuid;
    rsp_fields.num_uuids128 = 1;
    rsp_fields.uuids128_is_complete = 1;

    rc = ble_gap_adv_rsp_set_fields(&rsp_fields);
    if (rc != 0) {
        ESP_LOGW("BLE_ADV", "Error setting scan response data; rc=%d", rc);
        // Continue anyway, this is not critical
    }

    // Start advertising
    rc = ble_gap_adv_start(ble_addr_type, NULL, BLE_HS_FOREVER, &adv_params,
                           ble_gap_adv_event, NULL);
    if (rc != 0) {
        ESP_LOGE("BLE_ADV", "Error enabling advertisement; rc=%d", rc);
        return rc;
    }

    g_advertising_active = true;
    ESP_LOGI("BLE_ADV", "Advertising started with version %d", version);

    // Log the UUID for debugging
    ESP_LOGI("BLE_ADV", "UUID: %02X%02X%02X%02X-%02X%02X-%02X%02X-%02X%02X-%02X%02X%02X%02X%02X%02X",
             uuid128[0], uuid128[1], uuid128[2], uuid128[3],
             uuid128[4], uuid128[5], uuid128[6], uuid128[7],
             uuid128[8], uuid128[9], uuid128[10], uuid128[11],
             uuid128[12], uuid128[13], uuid128[14], uuid128[15]);

    return 0;
}

// Stop BLE advertising
int ble_stop_advertising(void)
{
    if (!g_advertising_active) {
        ESP_LOGW("BLE_ADV", "Advertising not active");
        return 0;
    }

    int rc = ble_gap_adv_stop();
    if (rc != 0) {
        ESP_LOGE("BLE_ADV", "Error stopping advertisement; rc=%d", rc);
        return rc;
    }

    g_advertising_active = false;
    ESP_LOGI("BLE_ADV", "Advertising stopped");
    return 0;
}

// The infinite task
void host_task(void *param)
{
    nimble_port_run(); // This function will return only when nimble_port_stop() is executed
}

// Link循环任务实现
void link_cycle_task(void *param)
{
    ESP_LOGI("LINK_CYCLE", "Link cycle task started with UUID: %s, Version: %d, Name: %s",
             g_link_cycle_uuid.c_str(), g_link_cycle_version, g_link_cycle_device_name.c_str());

    while (g_link_cycle_active) {
        uint32_t cycle_start = xTaskGetTickCount() * portTICK_PERIOD_MS;

        ESP_LOGD("LINK_CYCLE", "=== Starting new 400ms cycle ===");

        // 阶段1: 扫描 (0-150ms)
        ESP_LOGD("LINK_CYCLE", "Phase 1: Scanning for %d ms", LINK_SCAN_DURATION_MS);
        {
            std::lock_guard<std::mutex> lk(g_ble_results_mutex);
            g_ble_scan_results.clear();
        }

        // 停止当前广播（如果有的话）
        if (g_advertising_active) {
            ble_stop_advertising();
        }

        // 开始短时间扫描
        ble_app_scan_short(LINK_SCAN_DURATION_MS);

        // 等待扫描完成
        vTaskDelay(pdMS_TO_TICKS(LINK_SCAN_DURATION_MS + 10)); // 额外10ms确保扫描完成

        // 阶段2: 随机back-off (150-170ms)
        uint32_t backoff_ms = esp_random() % (LINK_BACKOFF_MAX_MS + 1); // 0-20ms
        ESP_LOGD("LINK_CYCLE", "Phase 2: Random back-off for %lu ms", backoff_ms);
        if (backoff_ms > 0) {
            vTaskDelay(pdMS_TO_TICKS(backoff_ms));
        }

        // 阶段3: 广播 (170-400ms)
        uint32_t elapsed = (xTaskGetTickCount() * portTICK_PERIOD_MS) - cycle_start;
        uint32_t adv_duration = LINK_CYCLE_DURATION_MS - elapsed;

        if (adv_duration > 10) { // 至少广播10ms
            ESP_LOGD("LINK_CYCLE", "Phase 3: Advertising for %lu ms", adv_duration);

            // 开始广播
            int result = ble_start_advertising_cpp(g_link_cycle_uuid, g_link_cycle_version, g_link_cycle_device_name);
            if (result != 0) {
                ESP_LOGW("LINK_CYCLE", "Failed to start advertising: %d", result);
            }

            // 等待广播时间结束
            vTaskDelay(pdMS_TO_TICKS(adv_duration));

            // 停止广播
            if (g_advertising_active) {
                ble_stop_advertising();
            }
        } else {
            ESP_LOGW("LINK_CYCLE", "Not enough time for advertising phase, skipping");
        }

        // 确保总周期时间为400ms
        uint32_t total_elapsed = (xTaskGetTickCount() * portTICK_PERIOD_MS) - cycle_start;
        if (total_elapsed < LINK_CYCLE_DURATION_MS) {
            uint32_t remaining = LINK_CYCLE_DURATION_MS - total_elapsed;
            ESP_LOGD("LINK_CYCLE", "Waiting additional %lu ms to complete 400ms cycle", remaining);
            vTaskDelay(pdMS_TO_TICKS(remaining));
        }

        ESP_LOGD("LINK_CYCLE", "=== Cycle completed ===");
    }

    // 清理：停止广播
    if (g_advertising_active) {
        ble_stop_advertising();
    }

    ESP_LOGI("LINK_CYCLE", "Link cycle task stopped");
    g_link_cycle_task_handle = nullptr;
    vTaskDelete(NULL);
}

// 启动link循环
int start_link_cycle(void)
{
    return start_link_cycle_with_config(g_link_cycle_uuid.c_str(), g_link_cycle_version, g_link_cycle_device_name.c_str());
}

// 启动link循环（带配置）
int start_link_cycle_with_config(const char* uuid128_hex, uint8_t version, const char* device_name)
{
    if (g_link_cycle_active) {
        ESP_LOGW("LINK_CYCLE", "Link cycle already active");
        return -1;
    }

    // 验证UUID格式
    if (strlen(uuid128_hex) != 32) {
        ESP_LOGE("LINK_CYCLE", "UUID must be exactly 32 hex characters");
        return -1;
    }

    // 保存配置
    g_link_cycle_uuid = std::string(uuid128_hex);
    g_link_cycle_version = version;
    g_link_cycle_device_name = std::string(device_name);

    g_link_cycle_active = true;

    BaseType_t result = xTaskCreate(
        link_cycle_task,
        "link_cycle",
        4096,  // 栈大小
        NULL,
        5,     // 优先级
        &g_link_cycle_task_handle
    );

    if (result != pdPASS) {
        ESP_LOGE("LINK_CYCLE", "Failed to create link cycle task");
        g_link_cycle_active = false;
        return -1;
    }

    ESP_LOGI("LINK_CYCLE", "Link cycle started successfully");
    return 0;
}

// 停止link循环
int stop_link_cycle(void)
{
    if (!g_link_cycle_active) {
        ESP_LOGW("LINK_CYCLE", "Link cycle not active");
        return 0;
    }

    ESP_LOGI("LINK_CYCLE", "Stopping link cycle...");
    g_link_cycle_active = false;

    // 等待任务结束
    if (g_link_cycle_task_handle != nullptr) {
        // 等待最多1秒让任务自然结束
        for (int i = 0; i < 10 && g_link_cycle_task_handle != nullptr; i++) {
            vTaskDelay(pdMS_TO_TICKS(100));
        }

        // 如果任务还没结束，强制删除
        if (g_link_cycle_task_handle != nullptr) {
            ESP_LOGW("LINK_CYCLE", "Force deleting link cycle task");
            vTaskDelete(g_link_cycle_task_handle);
            g_link_cycle_task_handle = nullptr;
        }
    }

    // 确保停止广播
    if (g_advertising_active) {
        ble_stop_advertising();
    }

    ESP_LOGI("LINK_CYCLE", "Link cycle stopped");
    return 0;
}

} // extern "C"

// C++ convenience function for advertising
int ble_start_advertising_cpp(const std::string& uuid128_hex, uint8_t version, const std::string& device_name)
{
    if (uuid128_hex.length() != 32) {
        ESP_LOGE("BLE_ADV", "UUID must be exactly 32 hex characters (128 bits)");
        return -1;
    }

    // Convert hex string to bytes
    uint8_t uuid128[16];
    for (int i = 0; i < 16; i++) {
        std::string byte_str = uuid128_hex.substr(i * 2, 2);
        uuid128[i] = (uint8_t)strtol(byte_str.c_str(), NULL, 16);
    }

    return ble_start_advertising(uuid128, version, device_name.c_str());
}

// C++ convenience function for starting link cycle
int start_link_cycle_cpp(const std::string& uuid128_hex, uint8_t version, const std::string& device_name)
{
    return start_link_cycle_with_config(uuid128_hex.c_str(), version, device_name.c_str());
}
